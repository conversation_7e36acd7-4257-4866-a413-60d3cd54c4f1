/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/home/<USER>" |
       "/pages/about/about" |
       "/pages/dangerbank/dangertypelist" |
       "/pages/dangerbank/detail" |
       "/pages/dangerbank/index" |
       "/pages/dangerlist/CheckEventDealReview" |
       "/pages/dangerlist/detail" |
       "/pages/dangerlist/index" |
       "/pages/dangerlist/ReviewCheck" |
       "/pages/dataanalysis/index" |
       "/pages/demoCameraApp/index" |
       "/pages/error/error" |
       "/pages/hazardPhoto/common-position" |
       "/pages/hazardPhoto/createcheck" |
       "/pages/hazardPhoto/detail" |
       "/pages/hazardPhoto/index" |
       "/pages/map/index" |
       "/pages/preImg/preImg" |
       "/pages/randomcheck/createcheck" |
       "/pages/randomcheck/createhiddendanger" |
       "/pages/randomcheck/createhiddendanger1" |
       "/pages/randomcheck/createhiddendanger2" |
       "/pages/randomcheck/detail" |
       "/pages/randomcheck/index" |
       "/pages/randomcheck/legend" |
       "/pages/reviewBatch/dangercheck" |
       "/pages/reviewBatch/detail" |
       "/pages/reviewBatch/index" |
       "/pages/reviewdanger/dangercheck" |
       "/pages/reviewdanger/detail" |
       "/pages/reviewdanger/index" |
       "/pages/statisticalReport/index" |
       "/pages/task/checkRecord" |
       "/pages/task/clockIn" |
       "/pages/task/clockIn2" |
       "/pages/task/clockIn3" |
       "/pages/task/detail" |
       "/pages/task/equipmentcheckDetail" |
       "/pages/task/historyList" |
       "/pages/task/index" |
       "/pages/task/qrcodePage" |
       "/pages/task/qrcodePage2" |
       "/pages/task/scanCodes" |
       "/pages/taskFXDetail/index" |
       "/pages/typical/detail" |
       "/pages/typical/index" |
       "/pages/task/productionInspect/detail" |
       "/pages/task/productionInspect/equipentcheckItme" |
       "/pages/task/productionInspect/index" |
       "/pages/task/productionInspect/indexNeed" |
       "/pages/typical/add/custom-form" |
       "/pages/typical/add/index" |
       "/pages/typical/add/indexOld" |
       "/pages-safety/danger/index" |
       "/pages-safety/home/<USER>" |
       "/pages-safety/measures/gas-analysis" |
       "/pages-safety/measures/index" |
       "/pages-safety/measures/measures-disclose" |
       "/pages-safety/work/index";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
